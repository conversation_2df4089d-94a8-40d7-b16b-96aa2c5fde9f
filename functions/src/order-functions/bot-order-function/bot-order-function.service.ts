import * as admin from "firebase-admin";
import {
  AppDate,
  GiftFromBot,
  GiftStatus,
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderStatus,
  formatDateToFirebaseTimestamp,
} from "../../mikerudenko/marketplace-shared";
import { validateBotTokenSimple } from "../../services/bot-validation.service";
import { createGift, updateGiftOwnership } from "../../services/gift-service";
import { transferNetAmountToSeller } from "../../services/purchase-fee-processing-service";
import { bpsToDecimal, safeMultiply } from "../../utils";
import {
  throwBotTokenRequired,
  throwInvalidBotToken,
  throwInvalidOrderId,
  throwInvalidOrderStatus,
  throwOrderNotFound,
} from "./bot-order-function.error-handler";
import { getUserById } from "@/services/user-lookup.service";

async function handlePaidOrderGiftFlow(
  order: OrderEntity,
  gift: GiftFromBot,
  sellerTgId: string,
  orderDoc: admin.firestore.DocumentSnapshot
) {
  const db = admin.firestore();
  const batch = db.batch();

  // Create gift in separate collection
  const giftId = await createGift(gift, sellerTgId, order.collectionId, batch);

  // Update order status and reference gift by ID
  batch.update(orderDoc.ref, {
    giftId,
    gift, // Keep for backward compatibility
    status: OrderStatus.GIFT_SENT_TO_RELAYER,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
  });

  // Transfer funds from buyer's locked balance to seller
  if (order.buyerId && order.sellerId) {
    // Calculate net amount using order's fee structure
    const purchaseFeeRate = order.fees?.purchase_fee || 0;
    const netSellerAmount = safeMultiply(
      order.price,
      1 - bpsToDecimal(purchaseFeeRate)
    ); // Convert BPS to decimal

    // Use reusable function for money transfer
    await transferNetAmountToSeller(order, netSellerAmount, order.buyerId);
  }

  await batch.commit();

  return {
    success: true,
    message: "Gift sent to relayer successfully.",
    order: {
      ...order,
      giftId,
      gift,
      status: OrderStatus.GIFT_SENT_TO_RELAYER,
    },
  };
}

async function handleCreatedOrderGiftFlow(
  order: OrderEntity,
  gift: GiftFromBot,
  sellerTgId: string,
  orderDoc: admin.firestore.DocumentSnapshot
) {
  // Create gift in separate collection
  const giftId = await createGift(gift, sellerTgId, order.collectionId);

  await orderDoc.ref.update({
    giftId,
    gift, // Keep for backward compatibility
    status: OrderStatus.ACTIVE,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
  });

  return {
    success: true,
    message: "Gift added and order activated successfully.",
    order: {
      ...order,
      giftId,
      gift,
      status: OrderStatus.ACTIVE,
    },
  };
}

async function handleDirectGiftDepositFlow(
  gift: GiftFromBot,
  userTgId: string,
  collectionId?: string
) {
  // Create gift in separate collection without order association
  const giftId = await createGift(gift, userTgId, collectionId || "");

  return {
    success: true,
    message: "Gift deposited successfully.",
    giftId,
  };
}

export function validateBotToken(botToken?: string): void {
  try {
    validateBotTokenSimple(botToken);
  } catch (error) {
    if (
      error instanceof Error &&
      error.message.includes("Bot token is required")
    ) {
      throwBotTokenRequired();
    } else {
      throwInvalidBotToken();
    }
  }
}

export function validateOrderId(orderId?: string): void {
  if (!orderId) {
    throwInvalidOrderId();
  }
}

export async function completePurchaseForBot(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    throwInvalidOrderStatus();
  }

  await orderDoc.ref.update({
    status: OrderStatus.FULFILLED,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
  });

  // Transfer gift ownership to buyer when order becomes fulfilled
  if (order.giftId && order.buyerId) {
    try {
      const buyerTgId = (await getUserById(order.buyerId))?.tg_id;
      if (!buyerTgId) {
        throw new Error("Buyer telegram ID not found");
      }
      await updateGiftOwnership(order.giftId, buyerTgId, GiftStatus.WITHDRAWN);
    } catch (error) {
      console.warn("Failed to transfer gift ownership:", error);
    }
  }

  return {
    success: true,
    message: "Purchase completed successfully.",
    order: {
      ...order,
      status: OrderStatus.FULFILLED,
    },
  };
}

export async function sendGiftToRelayerForBot(params: {
  orderId: string;
  gift: GiftFromBot;
  botToken: string;
}) {
  const { orderId, gift, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Get seller's telegram ID for gift ownership
  if (!order.sellerId) {
    throw new Error("Order has no seller ID");
  }

  const sellerTgId = (await getUserById(order.sellerId))?.tg_id;
  if (!sellerTgId) {
    throw new Error("Seller telegram ID not found");
  }

  // Handle different order statuses using dedicated flow handlers
  if (order.status === OrderStatus.PAID) {
    return await handlePaidOrderGiftFlow(order, gift, sellerTgId, orderDoc);
  } else if (order.status === OrderStatus.CREATED) {
    return await handleCreatedOrderGiftFlow(order, gift, sellerTgId, orderDoc);
  } else {
    throwInvalidOrderStatus();
  }
}

export async function depositGiftDirectlyForBot(params: {
  gift: GiftFromBot;
  userTgId: string;
  botToken: string;
  collectionId?: string;
}) {
  const { gift, userTgId, botToken, collectionId } = params;

  validateBotToken(botToken);

  if (!userTgId) {
    throw new Error("User telegram ID is required");
  }

  return await handleDirectGiftDepositFlow(gift, userTgId, collectionId);
}
