import { Telegraf } from "telegraf";
import {
  handleContactSupportButton,
  handleGetMyBuyOrdersButton,
  handleGetMySellOrdersButton,
  handleDepositGiftButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
  handleReceiveGiftCallback,
  handlePaidOrdersAwaitingGiftCallback,
  handleCreatedOrdersNeedingActivationCallback,
  handleCancelledOrdersWithGiftsCallback,
  handleViewBuyOrdersCallback,
  handleViewSellOrdersCallback,
} from "./handlers/callbacks/index";
import {
  handleHelpCommand,
  handleStartCommand,
  handleHealthCommand,
} from "./handlers/commands";
import { businessConnectionMiddleware } from "./middleware/business-connection";
import { logBotError } from "./bot.logger";

import { loadEnvironment } from "./config/env-loader";

loadEnvironment();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables list.");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(businessConnectionMiddleware);

bot.start(handleStartCommand);
bot.help(handleHelpCommand);
bot.command("health", handleHealthCommand);

bot.hears("🛒 My Buy Orders", handleGetMyBuyOrdersButton);
bot.hears("💰 My Sell Orders", handleGetMySellOrdersButton);
bot.hears("🎁 Deposit a Gift", handleDepositGiftButton);
bot.hears("📞 Contact Support", handleContactSupportButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action("view_buy_orders", handleViewBuyOrdersCallback);
bot.action("view_sell_orders", handleViewSellOrdersCallback);
bot.action("paid_orders_awaiting_gift", handlePaidOrdersAwaitingGiftCallback);
bot.action(
  "created_orders_needing_activation",
  handleCreatedOrdersNeedingActivationCallback
);
bot.action(
  "cancelled_orders_with_gifts",
  handleCancelledOrdersWithGiftsCallback
);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action(/^receive_gift_(.+)$/, handleReceiveGiftCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

bot.catch((err, ctx) => {
  logBotError({
    error: err,
    chatId: ctx?.chat?.id as number,
    userId: ctx?.from?.id as number,
  });
  ctx?.reply?.("Sorry, something went wrong. Please try again later.");
});

export default bot;
